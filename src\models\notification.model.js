const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const MongooseDelete = require('mongoose-delete');

const notificationSchema = mongoose.Schema(
  {
    receiver: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    message: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      enum: ['provider_to_customer', 'customer_to_provider'],
      required: true,
    },
    status: {
      type: String,
      enum: ['unread', 'read'],
      default: 'unread',
    },

    // ✅ New fields to link provider and product(s)
    provider: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Provider',
    },

    products: [
      {
        product: { type: mongoose.Schema.Types.ObjectId, ref: 'Product' },
        option: { type: mongoose.Schema.Types.ObjectId, ref: 'Option' },
      },
    ],
  },
  {
    timestamps: true,
  }
);


// Add plugins
notificationSchema.plugin(toJSON);
notificationSchema.plugin(MongooseDelete, { overrideMethods: 'all' });
notificationSchema.plugin(paginate);

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;

