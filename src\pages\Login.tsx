import { useState } from "react";
import { <PERSON><PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { Password } from "primereact/password";
import { useFormik } from "formik";
import * as Yup from "yup";
import { login as loginApi } from "../apis/auth";
import { useAuth } from "../utils/AuthContext";
import { catchAsync, showSuccess } from "../utils/helper";
import { Link } from "react-router-dom";
import { requestPermissionAndGetToken, setupForegroundMessageListener } from "../apis/permission";
import Logo from "../assets/logo.svg";

interface LoginFormValues {
    email: string;
    password: string;
}

const Login = () => {
    const { login } = useAuth();
    const [loading, setLoading] = useState<boolean>(false);
    const formik = useFormik<LoginFormValues>({
        initialValues: {
            email: "",
            password: "",
        },
        validationSchema: Yup.object({
            email: Yup.string().email("Invalid email format").required("Email is required"),
            password: Yup.string().min(6, "Password must be at least 6 characters").required("Password is required"),
        }),
        onSubmit: async (values) => {
            setLoading(true);

            await catchAsync(async () => {
                const response = await loginApi(values.email, values.password);

                if (response.status === true && response.data) {
                    const { provider } = response.data;
                    const { access, refresh } = response.data.tokens;
                    const { token: accessToken, expires: accessExpires } = access;
                    const { token: refreshToken, expires: refreshExpires } = refresh;
                    const { role } = response.data.user;
                    if (role === 'provider' || role === 'admin') {
                        showSuccess(response.message ?? "You have successfully logged in.");
                        if (role === "provider" && typeof provider != undefined && ('isApproved' in provider)) {
                            if (provider.isApproved === 'approved')
                                login(accessToken, refreshToken, accessExpires, refreshExpires, role, true, true);
                            else {
                                login(accessToken, refreshToken, accessExpires, refreshExpires, role, true);
                            }
                        }
                        else
                            login(accessToken, refreshToken, accessExpires, refreshExpires, role);
                    }

                    else{
                        throw new Error("User unauthenticated");
                    }

                    // Setup notifications after successful login
                    await requestPermissionAndGetToken();
                    setupForegroundMessageListener();

                } else {
                    throw new Error(response.message || "Login failed.");
                }
            });

            setLoading(false);
        },
    });

    return (
        <div className="h-screen flex justify-center items-start bg-gray-900">
            <div className="bg-gray-800 shadow-lg rounded-2xl p-6 w-full max-w-sm">
                <img className='h-18 w-18 rounded-full cursor-pointer rounded-full flex mx-auto mb-4' src={Logo} alt="Beepr Logo" />
                <h2 className="text-center text-2xl font-bold text-[#CE93D8] mb-4">Login</h2>
                <form onSubmit={formik.handleSubmit} className="space-y-5">
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                            Email
                        </label>
                        <InputText
                            id="email"
                            name="email"
                            type="email"
                            value={formik.values.email}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className="w-full mt-2 p-2"
                        />
                        {formik.touched.email && formik.errors.email ? (
                            <small className="text-red-400">{formik.errors.email}</small>
                        ) : null}
                    </div>

                    <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                            Password
                        </label>
                        <Password
                            id="password"
                            name="password"
                            value={formik.values.password}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            feedback={false}
                            toggleMask
                            className="w-full mt-2"
                            inputClassName="w-full p-2"
                        />
                        {formik.touched.password && formik.errors.password ? (
                            <small className="text-red-400">{formik.errors.password}</small>
                        ) : null}
                    </div>

                    <Button
                        type="submit"
                        label="Login"
                        className="w-full text-white py-2 rounded-lg transition"
                        disabled={loading}
                    />
                    <p className="mt-4 text-center text-gray-600">
                        Don't have an account?{' '}
                        <Link to="/signup" className="text-[#CE93D8] hover:underline">
                            Sign Up
                        </Link>
                    </p>
                </form>
            </div>
        </div>
    );
};

export default Login;
