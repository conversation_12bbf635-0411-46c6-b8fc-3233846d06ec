import React from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { useFormik, FieldArray, FormikProvider, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { activeOrInactiveOptions, CategoryFormData, SubCategoryFormData } from '../../types/global'; // Adjust types accordingly
import { Dropdown } from 'primereact/dropdown';
import { ToggleButton } from 'primereact/togglebutton';

interface CategoryFormProps {
  visible: boolean;
  onHide: () => void;
  onSubmit: (values: CategoryFormData, actions:FormikHelpers<CategoryFormData>) => void;
  initialValues: Partial<CategoryFormData>;
  editMode: boolean;
}

const validationSchema = Yup.object({
  name: Yup.string().required('Category name is required'),
  status: Yup.boolean().required('Category status is required'),
  subCategories: Yup.array().of(
    Yup.object({
      name: Yup.string().required('Subcategory name is required'),
      status: Yup.boolean().required('Subcategory status is required'),
    })
  ),
});

const CategoryForm: React.FC<CategoryFormProps> = ({
  visible,
  onHide,
  onSubmit,
  initialValues,
  editMode,
}) => {
  const formik = useFormik<CategoryFormData>({
    initialValues: {
      name: initialValues.name || '',
      // Use explicit check to allow false values without overriding them
      status: initialValues.status !== undefined ? initialValues.status : true,
      subCategories: (initialValues.subCategories as SubCategoryFormData[]) || [],
    },
    validationSchema,
    onSubmit: (values, actions) => {
      onSubmit(values, actions);
    },
    enableReinitialize: true,
  });

  return (
    <Dialog
      header={editMode ? 'Edit Category' : 'Create Category'}
      visible={visible}
      onHide={onHide}
      style={{ width: '50vw' }}
    >
      <FormikProvider value={formik}>
        <form onSubmit={formik.handleSubmit} className="p-fluid">
          {/* Category Name */}
          <div className="field">
            <label htmlFor="name">Category Name</label>
            <InputText
              id="name"
              name="name"
              value={formik.values.name}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            {formik.touched.name && formik.errors.name && (
              <small className="p-error">{formik.errors.name}</small>
            )}
          </div>

          {/* Category Status */}
          <div className="field">
            <label htmlFor="status">Category Status</label>
            <Dropdown
              id="status"
              name="status"
              value={formik.values.status}
              options={activeOrInactiveOptions}
              onChange={(e) => formik.setFieldValue('status', e.value)}
              onBlur={() => formik.setFieldTouched('status', true)}
              placeholder="Select category status"
            />
            {formik.touched.status && formik.errors.status && (
              <small className="p-error">{formik.errors.status}</small>
            )}
          </div>

          {/* Subcategories */}
          <div className="field">
            <label>Subcategories</label>
            <FieldArray
              name="subCategories"
              render={(arrayHelpers) => (
                <div>
                  {formik.values.subCategories && formik.values.subCategories.length > 0 ? (
                    formik.values.subCategories.map((subCategory, index) => (
                      <div key={index} className="p-mb-2">
                        <div className="p-inputgroup">
                          <InputText
                            name={`subCategories[${index}].name`}
                            value={subCategory.name}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            placeholder="Subcategory name"
                          />
                          <span className="p-inputgroup-addon">
                            <ToggleButton
                              checked={subCategory.status}
                              onChange={async () => {
                                const updatedSubCategories = [...formik.values.subCategories];
                                updatedSubCategories[index] = {
                                  ...updatedSubCategories[index],
                                  status: !subCategory.status,
                                };
                                formik.setFieldValue('subCategories', updatedSubCategories);
                              }}
                              onLabel="Active"
                              offLabel="Inactive"
                              onIcon="pi pi-check"
                              offIcon="pi pi-times"
                            />
                          </span>
                          <span className="p-inputgroup-addon">
                            {subCategory._id ? (
                              <Button
                                icon="pi pi-trash"
                                className="p-button-text p-button-danger"
                                type="button"
                                tooltip="Delete Subcategory"
                                onClick={() => arrayHelpers.remove(index)}
                              />
                            ) : (
                              <Button
                                icon="pi pi-minus-circle"
                                className="p-button-text p-button-danger"
                                type="button"
                                tooltip="Remove Subcategory"
                                onClick={() => arrayHelpers.remove(index)}
                              />
                            )}
                          </span>
                        </div>
                        {formik.touched.subCategories &&
                          formik.touched.subCategories[index] &&
                          formik.errors.subCategories &&
                          (formik.errors.subCategories as any)[index] &&
                          (formik.errors.subCategories as any)[index].name && (
                            <small className="p-error">
                              {(formik.errors.subCategories as any)[index].name}
                            </small>
                          )}
                      </div>
                    ))
                  ) : (
                    <div>No subcategories. You can add one below.</div>
                  )}
                  <div className="p-mt-2">
                    <Button
                      type="button"
                      label="Add Subcategory"
                      icon="pi pi-plus-circle"
                      onClick={() => arrayHelpers.push({ name: '', status: true })}
                    />
                  </div>
                </div>
              )}
            />
            {formik.touched.subCategories &&
              typeof formik.errors.subCategories === 'string' && (
                <small className="p-error">{formik.errors.subCategories}</small>
              )}
          </div>

          <div className="field">
            <Button
              type="submit"
              label={editMode ? 'Update Category' : 'Create Category'}
              className="p-mt-2"
            />
          </div>
        </form>
      </FormikProvider>
    </Dialog>
  );
};

export default CategoryForm;
