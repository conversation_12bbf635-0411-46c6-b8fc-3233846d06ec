const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { trim } = require('validator');

const userPreferenceSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User', // Reference to User model
      required: true,
    },
    question: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question', // Reference to Question model
      required: true,
    },
    answer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Option', // Reference to Question model
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add plugin that converts mongoose to json
userPreferenceSchema.plugin(toJSON);
userPreferenceSchema.plugin(paginate);

const UserPreference = mongoose.model('userPreference', userPreferenceSchema);
module.exports = UserPreference;
