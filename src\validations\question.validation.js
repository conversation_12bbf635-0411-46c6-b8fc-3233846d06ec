const Joi = require('joi');
const { Question, Option } = require('../models');
const { exists, unique, objectId } = require('./custom.validation');

const question = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Question, field: '_id', value });
    return value;
  });

const option = Joi.string()
  .custom(objectId)
  .required()
  .external(async (value) => {
    await exists({ model: Option, field: '_id', value });
    return value;
  });

const params = {
  params: Joi.object({
    question,
  }),
};

const body = {
  body: Joi.object({
    question,
  }),
};

const create = {
  body: Joi.object({
    value: Joi.string().required().trim().max(1000).external(async (value) => {
        await unique({ model: Question, field: 'value', value });
        return value;
      }),
    sequence: Joi.number()
      .required()
      .external(async (value) => {
        await unique({ model: Question, field: 'sequence', value });
        return value;
      }),
    options: Joi.array()
      .items(
        Joi.object({
          value: Joi.string().required().trim().max(1000),
        })
      )
      .min(2)
      .required(),
  }),
};

const update = {
  body: Joi.object({
    question,
    value: Joi.string().required().trim().max(1000)
    .external(async (value, helpers) => {
      const { question } = helpers.state.ancestors[0];
      await unique({ model: Question, field: 'value', value, excludeId: question });
      return value;
    }),
    sequence: Joi.number()
      .required()
      .external(async (value, helpers) => {
        const { question } = helpers.state.ancestors[0];
        await unique({ model: Question, field: 'sequence', value, excludeId: question });
        return value;
      }),
    options: Joi.array()
      .items(
        Joi.object({
          _id: option,
          value: Joi.string()
            .required()
            .trim()
            .max(1000)
            .external(async (value, helpers) => {
              const { _id: optionId } = helpers.state.ancestors[0]; // Access option ID from the current object
              await unique({ model: Question, field: ['question', 'value'], value: [question, value], excludeId: optionId });
              return value;
            }),
        })
      )
      .min(2)
      .required(),
  }),
};

const createOptions = {
  body: Joi.object({
    question,
    options: Joi.array()
      .items(
        Joi.object({
          value: Joi.string()
            .required()
            .trim()
            .max(1000)
            .external(async (value, helpers) => {
              const { question } = helpers.state.ancestors[0];
              await unique({ model: Question, field: ['question', 'value'], value: [question, value] });
              return value;
            }),
        })
      )
      .min(1)
      .required(),
  }),
};

const updateOption = {
  body: Joi.object({
    question,
    option,
    value: Joi.string()
      .required()
      .trim()
      .max(1000)
      .external(async (value, helpers) => {
        const { question } = helpers.state.ancestors[0]; // Access option ID from the current object
        const { option } = helpers.state.ancestors[0]; // Access option ID from the current object
        await unique({ model: Question, field: ['question', 'value'], value: [question, value], excludeId: option });
        return value;
      }),
  }),
};

const deleteOption = {
  body: Joi.object({
    option,
  }),
};

const preference = {
  body: Joi.object({
    question,
    answer:option,
  })
};

module.exports = {
  params,
  body,
  create,
  update,
  option,
  createOptions,
  updateOption,
  deleteOption,
  preference
};
