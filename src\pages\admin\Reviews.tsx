import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { But<PERSON> } from 'primereact/button';
import { Column } from 'primereact/column';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import { Rating } from 'primereact/rating';
import { Dialog } from 'primereact/dialog';
import usePagination from '../../hooks/usePagination';
import { getReviews } from '../../apis/admin';
import { catchAsync } from '../../utils/helper';
import { Review, defaultPaginationValues } from '../../types/global';

const Reviews: React.FC = () => {
    const [reviews, setReviews] = useState<Review[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [loading, setLoading] = useState(false);
    const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
    const [searchText, setSearchText] = useState('');
    const [selectedReview, setSelectedReview] = useState<Review | null>(null);
    const [experienceDialogVisible, setExperienceDialogVisible] = useState(false);

    useEffect(() => {
        fetchReviews();
    }, [params]);

    const fetchReviews = async () => {
        setLoading(true);
        catchAsync(async () => {
            const response = await getReviews({
                page: params.page,
                limit: params.limit,
                sort: params.sortField && params.sortOrder 
                    ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}` 
                    : undefined,
                search: params.search,
            });
            setReviews(response.data.results);
            setTotalRecords(response.data.totalResults);
        }).finally(() => setLoading(false));
    };

    const handleSearch = () => {
        setSearch(searchText);
    };

    // Template functions
    const ratingBodyTemplate = (rowData: Review) => {
        return <Rating value={rowData.rating} readOnly cancel={false} />;
    };

    const reviewedOnBodyTemplate = (rowData: Review) => {
        return new Date(rowData.reviewed_on).toLocaleDateString();
    };

    const experienceBodyTemplate = (rowData: Review) => {
        const experience = rowData.experience;
        const truncated = experience.length > 50 ? experience.substring(0, 50) + '...' : experience;
        
        return (
            <div>
                <span>{truncated}</span>
                {experience.length > 50 && (
                    <Button
                        label="Read More"
                        className="p-button-link p-button-sm"
                        onClick={() => {
                            setSelectedReview(rowData);
                            setExperienceDialogVisible(true);
                        }}
                    />
                )}
            </div>
        );
    };

    const customerBodyTemplate = (rowData: Review) => {
        return rowData.customer_name;
    };

    return (
        <div>
            <div className="flex justify-between align-items-center">
                <h2>All Reviews</h2>
                 <div className="flex justify-between items-center mb-4">
                    <IconField iconPosition="left">
                        <InputIcon className="pi pi-search" />
                        <InputText
                            value={searchText}
                            onChange={(e) => setSearchText(e.target.value)}
                            placeholder="Search reviews..."
                            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                        />
                    </IconField>
               
                </div>
            </div>

            <div className=" rounded-lg shadow">
               

                <DataTable
                    value={reviews}
                    lazy
                    paginator
                    first={(params.page - 1) * params.limit}
                    rows={params.limit}
                    totalRecords={totalRecords}
                    onPage={onPage}
                    onSort={onSort}
                    sortField={params.sortField}
                    sortOrder={params.sortOrder}
                    loading={loading}
                    rowsPerPageOptions={[10, 20, 50]}
                    className="p-datatable-striped"
                    removableSort
                >
                    <Column header="Rating" body={ratingBodyTemplate} sortable />
                    <Column field="title" header="Title" sortable />
                    <Column header="Experience" body={experienceBodyTemplate} />
                    <Column field="product_name" header="Product" sortable />
                    <Column header="Customer" body={customerBodyTemplate} sortable />
                    <Column field="order_id" header="Order ID" sortable />
                    <Column header="Reviewed On" body={reviewedOnBodyTemplate} sortable />
                </DataTable>
            </div>

            {/* Experience Dialog */}
            <Dialog
                header="Full Experience"
                visible={experienceDialogVisible}
                style={{ width: '50vw' }}
                onHide={() => setExperienceDialogVisible(false)}
            >
                {selectedReview && (
                    <div>
                        <h4>{selectedReview.title}</h4>
                        <p><strong>Rating:</strong> <Rating value={selectedReview.rating} readOnly cancel={false} /></p>
                        <p><strong>Product:</strong> {selectedReview.product_name}</p>
                        <p><strong>Customer:</strong> {selectedReview.customer_name}</p>
                        <p><strong>Experience:</strong></p>
                        <p>{selectedReview.experience}</p>
                    </div>
                )}
            </Dialog>
        </div>
    );
};

export default Reviews;
