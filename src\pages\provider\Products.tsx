import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { <PERSON><PERSON> } from 'primereact/button';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Column } from 'primereact/column';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import { Dialog } from 'primereact/dialog';
import usePagination from '../../hooks/usePagination';
import {
    getProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    getCategories,
    getQuestions,
} from '../../apis/provider';
import { catchAsync, handelFormData, handelResponse } from '../../utils/helper';
import ProductForm from '../../components/admin/ProductForm';
import {
    Category,
    Product,
    ProductFormData,
    Question,
    defaultPaginationValues,
    mapProductToFormData,
} from '../../types/global';
import { FormikHelpers } from 'formik';

const Products: React.FC = () => {
    const [products, setProducts] = useState<Product[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [loading, setLoading] = useState(false);
    const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
    const [searchText, setSearchText] = useState('');
    const [visibleDialog, setVisibleDialog] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    const [formData, setFormData] = useState<Partial<ProductFormData>>({});
    const [categories, setCategories] = useState<Category[]>([]);
    const [questions, setQuestions] = useState<Question[]>([]);
    
    // New states for image viewer dialog
    const [imagePopupVisible, setImagePopupVisible] = useState(false);
    const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);

    useEffect(() => {
        fetchProducts();
        fetchCategories();
        fetchQuestions();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [params]);

    // Fetch categories
    const fetchCategories = async () => {
        const response = await catchAsync(async () => {
            return await getCategories();
        });
        if (response && response.data) {
            setCategories(response.data);
        } else {
            setCategories([]);
        }
    };

    // Fetch questions
    const fetchQuestions = async () => {
        const response = await catchAsync(async () => {
            return await getQuestions();
        });
        if (response && response.data) {
            setQuestions(response.data);
        } else {
            setQuestions([]);
        }
    };

    // Fetch products
    const fetchProducts = async () => {
        setLoading(true);
        catchAsync(async () => {
            const response = await getProducts({
                page: params.page,
                limit: params.limit,
                sort:
                    params.sortField && params.sortOrder
                        ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}`
                        : undefined,
                search: params.search,
            });
            setProducts(response.data.results);
            setTotalRecords(response.data.totalResults);
        }).finally(() => setLoading(false));
    };

    const handleCreate = () => {
        setSelectedProduct(null);
        setFormData({});
        setEditMode(false);
        setVisibleDialog(true);
    };

    const handleEdit = (product: Product) => {
        setSelectedProduct(product);
        
        // Find the category ID based on product's categoryName
        const category = categories.find(c => c.name === product.categoryName);
        const categoryId = category?._id || '';
        
        // Find the subcategory ID within the category's subcategories
        const subCategory = category?.subCategories?.find(sc => sc.name === product.subCategoryName);
        const subCategoryId = subCategory?._id || '';
        
        // Map product to form data and set category/subcategory IDs
        const mappedFormData = {
            ...mapProductToFormData(product),
            category: categoryId,
            subCategory: subCategoryId,
        };
        
        setFormData(mappedFormData);
        setEditMode(true);
        setVisibleDialog(true);
    };

    const handleDelete = (productId: string) => {
        confirmDialog({
            message: 'Are you sure you want to delete this product?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                catchAsync(async () => {
                    const response = await deleteProduct(productId);
                    handelResponse(response);
                    if (response.status) fetchProducts();
                });
            },
        });
    };

    const handleSubmit = async (values: ProductFormData, actions: FormikHelpers<ProductFormData>) => {
        const filteredValues = {
            ...values,
            productUsages: values.productUsages.filter(u => u.option)
          };
        if (editMode) filteredValues.product = selectedProduct?._id;
        const formDataToSend = handelFormData(filteredValues);
        catchAsync(async () => {
            let response;
            if (editMode) {
                response = await updateProduct(formDataToSend);
            } else {
                response = await createProduct(formDataToSend);
            }
            handelResponse(response, actions);
            if (response.status) {
                setVisibleDialog(false);
                fetchProducts();
            }
        });
    };

    // Handle image click to show in dialog
    const handleImageClick = (url: string) => {
        setSelectedImageUrl(url);
        setImagePopupVisible(true);
    };

    // Column: Show image links for each image in the photo array
    const imageBodyTemplate = (rowData: Product) => {
        return rowData.photo && rowData.photo.length > 0 ? (
            <div className="flex flex-col gap-2">
                {rowData.photo.map((url, index) => (
                    <a
                        key={index}
                        className="text-blue-500 cursor-pointer"
                        onClick={() => handleImageClick(url)}
                    >
                        {`Image ${index + 1}`}
                    </a>
                ))}
            </div>
        ) : (
            'No Image'
        );
    };

    // Column: Count of variants
    const variantsCountBodyTemplate = (rowData: Product) => {
        return rowData.variants ? rowData.variants.length : 0;
    };

    // Column: Count of product usages
    const productUsagesCountBodyTemplate = (rowData: Product) => {
        return rowData.productUsages ? rowData.productUsages.length : 0;
    };

    // Column: Category name from sample data
    const categoryNameBodyTemplate = (rowData: Product) => {
        return rowData.categoryName || '';
    };

    // Column: Subcategory name from sample data
    const subCategoryNameBodyTemplate = (rowData: Product) => {
        return rowData.subCategoryName || '';
    };

    // Action column for Edit/Delete buttons
    const actionBodyTemplate = (rowData: Product) => {
        return (
            <div className="flex gap-2">
                <Button
                    icon="pi pi-pencil"
                    className="p-button-rounded p-button-success w-4 h-4"
                    onClick={() => handleEdit(rowData)}
                />
                <Button
                    icon="pi pi-trash"
                    className="p-button-rounded p-button-danger w-4 h-4"
                    onClick={() => handleDelete(rowData._id)}
                />
            </div>
        );
    };

    // Update search on input change with a debounce
    useEffect(() => {
        const timer = setTimeout(() => {
            setSearch(searchText);
        }, 500);
        return () => clearTimeout(timer);
    }, [searchText]);

    return (
        <div className="card p-fluid">
            <ConfirmDialog />

            <div className="flex justify-content-between align-items-center mb-4">
                <h2>Products Management</h2>
                <div className="flex gap-2">
                    <span className="p-input-icon-left">
                        <IconField iconPosition="left">
                            <InputIcon className="pi pi-search" />
                            <InputText
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                                placeholder="Search products..."
                            />
                        </IconField>
                    </span>
                    <Button label="Create Product" icon="pi pi-plus" onClick={handleCreate} />
                </div>
            </div>

            <DataTable
                value={products}
                lazy
                paginator
                first={(params.page - 1) * params.limit}
                rows={params.limit}
                totalRecords={totalRecords}
                onPage={onPage}
                onSort={onSort}
                sortField={params.sortField}
                sortOrder={params.sortOrder}
                loading={loading}
                rowsPerPageOptions={[10, 20, 50]}
                className="p-datatable-striped"
                removableSort
            >
                <Column header="Image" body={imageBodyTemplate} />
                <Column field="name" header="Name" sortable />
                <Column header="Variants" body={variantsCountBodyTemplate} sortable />
                <Column header="Product Usages" body={productUsagesCountBodyTemplate} sortable />
                <Column header="Category" body={categoryNameBodyTemplate} sortable />
                <Column header="Subcategory" body={subCategoryNameBodyTemplate} sortable />
                <Column header="Actions" body={actionBodyTemplate} />
            </DataTable>

            <ProductForm
                visible={visibleDialog}
                onHide={() => setVisibleDialog(false)}
                onSubmit={handleSubmit}
                initialValues={formData}
                editMode={editMode}
                categories={categories}
                questions={questions}
            />

            {/* Dialog to Display the Selected Image */}
            <Dialog
                visible={imagePopupVisible}
                onHide={() => setImagePopupVisible(false)}
                header="Image Viewer"
                style={{ minWidth: '60vw' }}
            >
                {selectedImageUrl ? (
                    <img
                        src={selectedImageUrl}
                        alt="Product"
                        style={{ width: '100%', maxHeight: '500px', objectFit: 'contain' }}
                    />
                ) : (
                    <p>No image available</p>
                )}
            </Dialog>
        </div>
    );
};

export default Products;
