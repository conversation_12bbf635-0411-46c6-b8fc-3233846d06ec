import { ProductFormData } from '../types/global';
import api from './index';

/**
 * Updates the provider profile.
 *
 * This function sends a PUT request with the given form data to update the provider's profile.
 *
 * @param {FormData} formData - The form data containing the profile updates.
 * @returns {Promise<any>} A promise that resolves with the response data.
 */
export const updateProfile = async (formData: FormData) => {
    const response = await api.put('provider/profile', formData);
    return response.data;
};

/**
 * Sets the provider profile.
 *
 * This function sends a POST request with the given form data to create or set the provider's profile.
 *
 * @param {FormData} formData - The form data containing the profile information.
 * @returns {Promise<any>} A promise that resolves with the response data.
 */
export const setProfile = async (formData: FormData) => {
    const response = await api.post('provider/profile', formData);
    return response.data;
};

/**
 * Retrieves the provider profile.
 *
 * This function sends a GET request to fetch the provider's profile information.
 *
 * @returns {Promise<any>} A promise that resolves with the profile data.
 */
export const getProfile = async () => {
    const response = await api.get('provider/profile');
    return response.data;
};

/**
 * Fetches a list of products with pagination.
 *
 * @param {object} params - The parameters for pagination and search.
 * @returns {Promise<any>} - A promise that resolves to the list of products.
 */
export const getProducts = async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`provider/product?${queryString}`);
    return response.data;
};

/**
 * Creates a new product.
 *
 * @param {FormData} formData - The form data containing product information.
 * @returns {Promise<any>} - A promise that resolves to the created product data.
 */
export const createProduct = async (formData: FormData | ProductFormData): Promise<any>  => {
    const response = await api.post(`provider/product`, formData)
    return response.data;
};

/**
 * Updates an existing product.
 *
 * @param {FormData} formData - The form data containing updated product information.
 * @returns {Promise<any>} - A promise that resolves to the updated product data.
 */
export const updateProduct = async (formData: FormData | ProductFormData): Promise<any>  => {
    const response = await api.put(`provider/product`, formData);
    return response.data;
};

/**
 * Deletes a product by its ID.
 *
 * @param {string} product - The ID of the product to be deleted.
 * @returns {Promise<any>} A promise that resolves to the response data from the delete operation.
 */
export const deleteProduct = async (product: string): Promise<any> => {
    // Note: For DELETE requests with a request body, Axios expects the body in the `data` property.
    const response = await api.delete(`provider/product`, { data: { product } });
    return response.data;
};

/**
 * Fetches a list of categories.
 *
 * @returns {Promise<any>} - A promise that resolves to the list of products.
 */
export const getCategories = async (): Promise<any> => {
    const response = await api.get(`common/category`);
    return response.data;
};

/**
 * Fetches a list of questions.
 *
 * @returns {Promise<any>} - A promise that resolves to the list of products.
 */
export const getQuestions = async (): Promise<any> => {
    const response = await api.get(`common/question`);
    return response.data;
};

// ################################################ Orders API ################################################

/**
 * Fetches a list of orders for the provider with pagination.
 *
 * @param {object} params - The parameters for pagination and search.
 * @returns {Promise<any>} - A promise that resolves to the list of orders.
 */
export const getOrders = async (providerId: string, params = {}): Promise<any> => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`provider/order/${providerId}?${queryString}`);
    return response.data;
};

/**
 * Updates an order status.
 *
 * @param {string} orderId - The ID of the order to update.
 * @param {string} status - The new status for the order.
 * @returns {Promise<any>} - A promise that resolves to the updated order data.
 */
export const updateOrderStatus = async (orderId: string, status: string): Promise<any> => {
    const response = await api.patch(`provider/order/status`, { orderId, status });
    return response.data;
};

// ################################################ Reviews API ################################################

/**
 * Fetches a list of reviews for the provider with pagination.
 *
 * @param {string} providerId - The ID of the provider.
 * @param {object} params - The parameters for pagination and search.
 * @returns {Promise<any>} - A promise that resolves to the list of reviews.
 */
export const getReviews = async (providerId: string, params = {}): Promise<any> => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`provider/reviews/${providerId}?${queryString}`);
    return response.data;
};