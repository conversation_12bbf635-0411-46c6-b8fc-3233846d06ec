import { FormikHelpers } from 'formik';
import { toast } from 'react-toastify';
import { CategoryFormData, CustomerFormData, ProductFormData, Provider, ProviderFormData, QuestionFormData, Response } from '../types/global';

interface CatchAsyncOptions {
    showToast: boolean; // Whether to show toast or not
}

export const catchAsync = async <T>(asyncFn: () => Promise<T>, options: CatchAsyncOptions = { showToast: true }): Promise<T | void> => {
    try {
        return await asyncFn();
    } catch (error: unknown) {
        let errorMessage = "An unknown error occurred.";

        if (error instanceof Error) {
            errorMessage = error.message;
        } else if (typeof error === "string") {
            errorMessage = error;
        } else if (typeof error === "object" && error !== null) {
            errorMessage = (error as any).message ?? JSON.stringify(error);
        }

        // Show toast only if `showToast` is true
        if (options.showToast) {
            toast.error(errorMessage);
        } else {
            console.error("API Error:", error);
        }

        return undefined;
    }
};

export const showSuccess = (message: string) => {
    toast.success(message || "Operation completed successfully");
};

export const showError = (message: string) => {
    toast.success(message || "Operation failed");
};

export const handelResponse = (response: Response, actions?: FormikHelpers<QuestionFormData> | FormikHelpers<CategoryFormData> | FormikHelpers<ProviderFormData> | FormikHelpers<Provider> | FormikHelpers<CustomerFormData> | FormikHelpers<ProductFormData>) => {
    if (response.status) {
        showSuccess(response.message);
    } else {
        if (response.code === 400 && !!actions) {
            actions.setErrors(response.data)
            // throw new Error(response.message);
        }
        // else{
        throw new Error(response.message);
        // }
    }
}

export const handelFormData = (
    values: Provider | ProviderFormData | CustomerFormData | ProductFormData
) => {
    const formData = new FormData();

    const appendFormData = (data: any, parentKey?: string): void => {
        if (data === undefined || data === null  || data === "") {
            // formData.append(parentKey!, '');
        } else if (isFile(data)) {
            formData.append(parentKey!, data);
        } else if (Array.isArray(data)) {
            if (data.length > 0 && data.every((item) => isFile(item))) {
                // Append each file using the key with square brackets (e.g., photos[])
                data.forEach((file) => {
                    formData.append(`${parentKey}[]`, file);
                });
            } else {
                data.forEach((item, index) => {
                    const key = parentKey ? `${parentKey}[${index}]` : String(index);
                    appendFormData(item, key);
                });
            }
        } else if (typeof data === 'object' && !(data instanceof Date)) {
            Object.keys(data).forEach((key) => {
                const newKey = parentKey ? `${parentKey}[${key}]` : key;
                appendFormData(data[key], newKey);
            });
        } else {
            formData.append(parentKey!, data.toString());
        }
    };

    Object.keys(values).forEach((key) => {
        appendFormData(values[key as keyof typeof values], key);
    });

    return formData;
};


function isFile(value: any): value is File {
    return value instanceof File;
}