import { <PERSON><PERSON><PERSON> } from "primereact/menubar";
import { Link, Outlet } from "react-router-dom";
import { useAuth } from "../utils/AuthContext";
import { showSuccess } from "../utils/helper";

const DefaultLayout = () => {
    const { isAuthenticated, logout, role } = useAuth();

    const start = (
        <div className="flex items-center gap-2">
            <Link to={"/"}>
                <h4>Beepr</h4>
            </Link>
        </div>
    );

    const handleLogout = () => {
        logout();
        showSuccess("Logged out successfully");
    };

    const end = (
        isAuthenticated ?
            <div className="flex items-center gap-4">
                <Link to={role === 'admin' ? "/admin" : "/dashboard"}> Dashboard </Link>
                <div className="cursor-pointer" onClick={() => handleLogout()}>Logout</div>
            </div>
            :
            <Link to={"/login"}> Login </Link>
    );

    return (
        <div className="min-h-screen flex flex-col">
            {/* Top Navbar */}
            <Menubar start={start} end={end} className="px-3" />

            {/* Main Content */}
            <main className="flex-1 p-4">
                <Outlet />
            </main>
        </div>
    );
};

export default DefaultLayout;
