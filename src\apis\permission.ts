import { messaging } from '../firebase'; // Adjust the import path as necessary
import { getToken, onMessage } from 'firebase/messaging';


const getAccessToken = () => localStorage.getItem("accessToken");

const requestPermissionAndGetToken = async (): Promise<void> => {
  console.log('Requesting notification permission...');
  try {
    const permission = await Notification.requestPermission();

    if (permission === 'granted') {
      console.log('Notification permission granted.');
      const fcmToken = await getToken(messaging, {
        vapidKey: "BEZjB19Zi_ILMovj5qf6D5sIm4o8fj0yCfe_K5hxF2RuCjQkjkrbHHGFMwVgaQUy9wXkXBz43aImFBXeURnu1P8", // Required only for Web. (In Firebase Console → Project Settings → Cloud Messaging → Web Push certificates)
      });
      if (fcmToken) {
        console.log('FCM Token:', fcmToken);

        // Store token locally for easy access
        storeFCMToken(fcmToken);

        const token = getAccessToken();
        // Send this token to your backend server and save it
        const response = await fetch('http://localhost:3011/v1/provider/save-fcm-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`, // If using auth
          },
          body: JSON.stringify({
            fcmToken: fcmToken,
          }),
        });
        if (!response.ok) {
          console.error('Failed to save FCM token:', response.statusText);
          const errorText = await response.text();
          console.error('Error details:', errorText);
        } else {
          console.log('FCM token saved successfully.');
          const responseData = await response.json();
          console.log('Save token response:', responseData);
        }
      } else {
        console.log('Failed to get FCM token.');
      }
    } else {
      console.log('Notification permission denied.');
    }
  } catch (error) {
    console.error('Error requesting notification permission or getting FCM token:', error);
  }
};

export const setupForegroundMessageListener = (): void => {
  console.log('Setting up foreground message listener...');

  // Check if messaging is available
  if (!messaging) {
    console.error('Firebase messaging is not available');
    return;
  }

  console.log('Firebase messaging is available, setting up onMessage listener...');

  try {
    onMessage(messaging, (payload) => {
      console.log('🔔 Message received in foreground: ', payload);
      console.log('Payload details:', JSON.stringify(payload, null, 2));

      // Show notification when app is in foreground
      if (payload.notification) {
        const { title, body, icon } = payload.notification;
        console.log('Showing foreground notification:', { title, body, icon });

        // Create a custom notification or use browser's notification API
        if (Notification.permission === 'granted') {
          const notification = new Notification(title || 'New Message', {
            body: body || 'You have a new message',
            icon: icon || '/favicon.ico',
            tag: 'foreground-notification',
            requireInteraction: true,
            silent: false
          });

          // Optional: Handle notification click
          notification.onclick = function(event) {
            console.log('Foreground notification clicked');
            event.preventDefault();
            window.focus(); // Focus the browser window
            notification.close();
          };

          notification.onshow = function() {
            console.log('Foreground notification shown');
          };

          notification.onerror = function(error) {
            console.error('Foreground notification error:', error);
          };

          // Auto close after 8 seconds
          setTimeout(() => {
            notification.close();
            console.log('Foreground notification auto-closed');
          }, 8000);
        } else {
          console.warn('Notification permission not granted, cannot show notification');
        }
      }

      // Also handle any custom data from your backend
      if (payload.data) {
        console.log('Custom data received:', payload.data);
        // You can handle custom data here if needed
      }
    });

    console.log('✅ Foreground message listener setup complete');
  } catch (error) {
    console.error('Error setting up foreground message listener:', error);
  }
};



// Store FCM token locally for easy access
const storeFCMToken = (token: string): void => {
  localStorage.setItem('fcmToken', token);
};

// Export the functions
export { requestPermissionAndGetToken, storeFCMToken };