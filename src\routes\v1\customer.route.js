const express = require('express');
const validate = require('../../middlewares/validate');
const { customerController, productController, questionController, providerController , orderController, reviewController , favouriteController, notifyController } = require('../../controllers');
const { customerValidation, questionValidation, productValidation , orderValidation ,reviewValidation , favouriteValidation , notificationValidation, providerValidation } = require('../../validations');
const auth = require('../../middlewares/auth');
const fileUpload = require('../../middlewares/fileUpload');

const router = express.Router();

// ############## Profile Routes ##############
router.patch('/profile/username', auth('customerRootAccess'), validate(customerValidation.username), customerController.username);
router.patch('/profile', auth('customerRootAccess'),
  fileUpload('uploads/customers', [{
    fieldName: 'photo',
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'],
    minSize: 20 * 1024,
    maxSize: 2000 * 1024,
    fileRequired: false,
    multiple: false
  }]),
  validate(customerValidation.profile),
  customerController.updateCustomer
);
router.patch('/profile/address', auth('customerRootAccess'), validate(customerValidation.customerAddress), customerController.updateCustomerAddress);
router.patch('/profile/notification', auth('customerRootAccess'), validate(customerValidation.notification), customerController.updateCustomer);
router.patch('/profile/update', auth('customerRootAccess'), (req, res, next) => { req.body.user = req.user._id; next(); }, validate(customerValidation.updateProfile), customerController.updateCustomer);

router.post('/preference', auth('customerRootAccess'), validate(questionValidation.preference), questionController.preference);
router.get('/preferences', auth('customerRootAccess'), questionController.getUserPreferences);

router.get('/providers', auth('customerRootAccess'), providerController.index);
router.get('/product/search', auth('customerRootAccess'), productController.search);
router.get('/providers/nearby', auth('customerRootAccess'), customerController.getNearbyProviders);
router.get('/provider/:provider', auth('customerRootAccess'), validate(providerValidation.params), providerController.view);
router.get('/product/:product', auth('customerRootAccess'), validate(productValidation.params), productController.view);
router.get('/products', auth('customerRootAccess'), productController.index);
router.get('/providerproduct/:providerId', auth('customerRootAccess'), productController.getProductByProviderId);

router.get('/products/recommended', auth('customerRootAccess'), productController.getRecommendedProducts);


//################# orderRoutes ###################
router.post('/order', auth('customerRootAccess'), validate(orderValidation.create), orderController.create);
router.get('/orders/:order', auth('customerRootAccess'),  orderController.index);
router.get('/order/:order', auth('customerRootAccess'),  orderController.view);
router.patch('/order/status', auth('customerRootAccess'),validate(orderValidation.update),  orderController.updateStatus);

//################ reviewRoutes #####################
router.post('/review', auth('customerRootAccess'),validate(reviewValidation.create),  reviewController.create);
router.get('/reviews/user/:userId',auth('customerRootAccess'), reviewController.getReviews);
router.get('/reviews',auth('customerRootAccess'), reviewController.getReviews); // Get all reviews with query filters
router.patch('/review',auth('customerRootAccess'), reviewController.update);
router.delete('/review',auth('customerRootAccess'), reviewController.softDelete);

//################ favouriteRoutes #####################

router.post('/favourite',auth('customerRootAccess'), validate(favouriteValidation.createFavourite), favouriteController.create);
router.get('/favourites',auth('customerRootAccess'), favouriteController.getFavourites);
router.patch('/favourite/toggle',auth('customerRootAccess'), validate(favouriteValidation.toggleLike), favouriteController.toggleLike);

//#################notificationRoute #################
router.get('/save-fcm-token',auth('customerRootAccess'), notifyController.saveFcmToken);
router.patch('/notification/:notificationId',auth('customerRootAccess'),validate(notificationValidation.updateStatus), notifyController.updateNotificationStatus);
router.delete('/notification',auth('customerRootAccess'), notifyController.softDelete);

module.exports = router;