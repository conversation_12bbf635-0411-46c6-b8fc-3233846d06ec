// components/GoogleMap.tsx
import React, { useState, useEffect } from 'react';
import { GoogleMap, useLoadScript, MarkerF } from '@react-google-maps/api';
import { defaultLocation } from '../../types/global';
// import { InputText } from 'primereact/inputtext';

// Define the props interface for the component
interface GoogleMapComponentProps {
  lat: number;
  lng: number;
  editable?: boolean;
  radius: number;
  onLocationChange?: (lat: number, lng: number) => void;
}

// Map container style
const mapContainerStyle = {
  width: '100%',
  height: '400px',
};

const GoogleMapComponent: React.FC<GoogleMapComponentProps> = ({ radius, lat = defaultLocation.latiude, lng = defaultLocation.longitude, editable = false, onLocationChange }) => {
  const [markerPosition, setMarkerPosition] = useState({ lat, lng });
  const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(null);
  const [circle, setCircle] = useState<google.maps.Circle | null>(null);
  // const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  // const inputRef = useRef<HTMLInputElement | null>(null);

  const apiKey = import.meta.env.VITE_APP_GOOGLE_MAPS_API_KEY;

  // Check if API key is available
  if (!apiKey) {
    return (
      <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '4px', backgroundColor: '#fff3cd' }}>
        <h4>Google Maps API Key Missing</h4>
        <p>Please set VITE_APP_GOOGLE_MAPS_API_KEY in your environment variables.</p>
      </div>
    );
  }

  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: apiKey,
    libraries: ['places'], // Required for search functionality
  });


  // Handle marker drag event
  const handleMarkerDragEnd = (e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const newLat = e.latLng.lat();
      const newLng = e.latLng.lng();
      setMarkerPosition({ lat: newLat, lng: newLng });
      if (onLocationChange) onLocationChange(newLat, newLng);
    }
  };

  // Handle map click event to place a new marker
  const handleMapClick = (e: google.maps.MapMouseEvent) => {
    if (editable && e.latLng) {
      const newLat = e.latLng.lat();
      const newLng = e.latLng.lng();
      setMarkerPosition({ lat: newLat, lng: newLng });
      if (onLocationChange) onLocationChange(newLat, newLng);
    }
  };

  // Handle place selection from the search bar
  // const handlePlaceSelect = () => {
  //   if (autocompleteRef.current) {
  //     const place = autocompleteRef.current.getPlace();
  //     console.log(place);

  //     if (!place || !place.geometry || !place.geometry.location) {
  //       console.error("Selected place has no geometry or location data.");
  //       return;
  //     }

  //     const newLat = place.geometry.location.lat();
  //     const newLng = place.geometry.location.lng();
  //     setMarkerPosition({ lat: newLat, lng: newLng });
  //     if (onLocationChange) onLocationChange(newLat, newLng);
  //   }
  // };

  // Create or update the circle overlay whenever lat, lng, radius, or mapInstance changes
  useEffect(() => {
    if (!mapInstance) return;

    // If a circle already exists, remove it before creating a new one
    if (circle) {
      circle.setMap(null);
    }

    const newCircle = new google.maps.Circle({
      strokeColor: '#FF0000',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: '#FF0000',
      fillOpacity: 0.35,
      map: mapInstance,
      center: { lat, lng },
      radius: radius, // radius in meters
    });

    setCircle(newCircle);

    // Cleanup: remove the circle when the component unmounts or before re-running this effect
    return () => {
      newCircle.setMap(null);
    };
  }, [lat, lng, radius, mapInstance]);

  // Map Reloads When lat and lng Change
  useEffect(() => {
    setMarkerPosition({ lat, lng });
  }, [lat, lng]);

  if (loadError) {
    console.error('Google Maps load error:', loadError);
    return (
      <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '4px' }}>
        <h4>Error loading Google Maps</h4>
        <p>Please check your API key and internet connection.</p>
        <p>Error: {loadError.message}</p>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <p>Loading Google Maps...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Search Bar */}
      {/* <Autocomplete
        onLoad={(autocomplete) => (autocompleteRef.current = autocomplete)}
        onPlaceChanged={handlePlaceSelect}
      >
        <InputText
          ref={inputRef}
          placeholder="Search for a location..."
          style={{ width: '100%', marginBottom: '10px' }}
        />
      </Autocomplete> */}

      {/* Google Map */}
      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        zoom={12}
        center={markerPosition}
        onClick={handleMapClick}
        onLoad={(map) => setMapInstance(map)}
      >
        <MarkerF
          position={markerPosition}
          draggable={editable}
          onDragEnd={handleMarkerDragEnd}
        />
      </GoogleMap>
    </div>
  );
};

export default GoogleMapComponent;
