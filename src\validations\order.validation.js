const Joi = require('joi');
const { Product, User, Provider, Variant } = require('../models');
const { exists, objectId } = require('./custom.validation');

const user = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: User, field: '_id', value });
    return value;
  });

const variant = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Variant, field: '_id', value });
    return value;
  });
const product = Joi.string()
  .required()
  .custom(objectId)
  .external(async (value) => {
    await exists({ model: Product, field: '_id', value });
    return value;
  });

const provider = Joi.optional().external(async (value, helpers) => {
  if (value) {
    objectId(value, helpers);
    await exists({ model: Provider, field: '_id', value });
  }
  return value;
});

// Schemas
const params = {
  params: Joi.object({
    orderId: Joi.string().required().custom(objectId),
  }),
};

const create = {
  body: Joi.object({
    user_id: user,
    variant_id: variant,
    product_id: product,
    provider_id: provider,
    shipping_address: Joi.object({
      full_name: Joi.string().required(),
      phone_number: Joi.string().optional(),
      street: Joi.string().required(),
      landmark: Joi.string().optional(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      country: Joi.string().required().default('India'),
      pincode: Joi.string().required(),
      latitude: Joi.string().optional(),
      longitude: Joi.string().optional(),
    }),
    quantity: Joi.number().min(1).required(),
    price: Joi.number().min(0).required(),
    total_price: Joi.number().min(0).required(),
    delivery: Joi.string().valid('yes', 'no').default('yes'),
    status: Joi.string().valid('pending', 'completed', 'cancelled_by_user', 'cancelled_by_provider').default('pending'),
  }),
};

const update = {
  body: Joi.object({
    orderId: Joi.string().required().custom(objectId),
    status: Joi.string().valid('pending', 'completed', 'cancelled_by_user', 'cancelled_by_provider'),
    delivery: Joi.string().valid('yes', 'no'),
  }).min(1),
};

module.exports = {
  params,
  create,
  update,
};
