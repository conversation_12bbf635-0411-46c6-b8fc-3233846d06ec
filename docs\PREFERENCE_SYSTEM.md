# Preference-Based Product Recommendation System

## Overview

This system allows customers to answer preference questions and receive personalized product recommendations based on their answers. When providers add products, they also specify which customer preferences the product matches.

## How It Works

### 1. **Admin Setup (Questions & Options)**
Admins create questions with multiple choice options that help categorize customer preferences and product characteristics.

### 2. **Customer Preference Collection**
When customers first use the app, they answer preference questions. Their answers are stored and used for product recommendations.

### 3. **Provider Product Addition**
When providers add products, they specify which customer preferences their product satisfies by selecting question-option pairs.

### 4. **Recommendation Engine**
The system matches customer preferences with product characteristics to show personalized recommendations.

## API Endpoints

### Customer Endpoints

#### 1. Get All Questions (for preference setup)
```http
GET /api/v1/common/question
Authorization: Bearer <customer_token>
```

**Response:**
```json
{
  "status": "success",
  "message": "Questions fetched successfully!",
  "data": [
    {
      "_id": "question_id_1",
      "value": "What type of cuisine do you prefer?",
      "sequence": 1,
      "options": [
        {
          "_id": "option_id_1",
          "value": "Italian"
        },
        {
          "_id": "option_id_2", 
          "value": "Chinese"
        },
        {
          "_id": "option_id_3",
          "value": "Indian"
        }
      ]
    }
  ]
}
```

#### 2. Save Customer Preference
```http
POST /api/v1/customer/preference
Authorization: Bearer <customer_token>
Content-Type: application/json

{
  "question": "question_id_1",
  "answer": "option_id_1"
}
```

#### 3. Get Customer's Current Preferences
```http
GET /api/v1/customer/preferences
Authorization: Bearer <customer_token>
```

**Response:**
```json
{
  "status": "success",
  "message": "User preferences fetched successfully!",
  "data": {
    "preferences": [
      {
        "_id": "preference_id_1",
        "user": "user_id",
        "question": {
          "_id": "question_id_1",
          "value": "What type of cuisine do you prefer?",
          "sequence": 1
        },
        "answer": {
          "_id": "option_id_1",
          "value": "Italian"
        }
      }
    ],
    "completionStatus": {
      "isComplete": false,
      "totalQuestions": 5,
      "answeredQuestions": 1,
      "missingQuestions": [...]
    }
  }
}
```

#### 4. Get Recommended Products
```http
GET /api/v1/customer/products/recommended?page=1&limit=10
Authorization: Bearer <customer_token>
```

**Response:**
```json
{
  "status": "success",
  "message": "Recommended products fetched successfully!",
  "data": {
    "results": [
      {
        "_id": "product_id_1",
        "name": "Margherita Pizza",
        "description": "Classic Italian pizza",
        "matchScore": 3,
        "variants": [...],
        "productUsages": [...],
        "providerDetails": [...]
      }
    ],
    "page": 1,
    "limit": 10,
    "totalPages": 2,
    "totalResults": 15
  }
}
```

### Provider Endpoints

#### Add Product with Preferences
```http
POST /api/v1/provider/product
Authorization: Bearer <provider_token>
Content-Type: application/json

{
  "name": "Margherita Pizza",
  "description": "Classic Italian pizza with fresh mozzarella",
  "category": "category_id",
  "subCategory": "subcategory_id",
  "delivery": true,
  "minDeliveryAmount": 100,
  "shippingCharges": 50,
  "photo": ["image_url_1", "image_url_2"],
  "variants": [
    {
      "amount": 1,
      "unit": "Piece",
      "default": true,
      "fake_price": 300,
      "price": 250,
      "stock": 50
    }
  ],
  "productUsages": [
    {
      "question": "question_id_1",
      "option": "option_id_1"
    },
    {
      "question": "question_id_2", 
      "option": "option_id_5"
    }
  ]
}
```

## Database Schema

### UserPreference Model
```javascript
{
  user: ObjectId,        // Reference to User
  question: ObjectId,    // Reference to Question
  answer: ObjectId,      // Reference to Option (selected answer)
  createdAt: Date,
  updatedAt: Date
}
```

### ProductUsages Model
```javascript
{
  product: ObjectId,     // Reference to Product
  question: ObjectId,    // Reference to Question
  option: ObjectId,      // Reference to Option
  createdAt: Date,
  updatedAt: Date
}
```

## Implementation Flow

### Customer Onboarding Flow
1. Customer registers/logs in
2. System checks if customer has completed preferences
3. If not complete, show preference questions in sequence
4. Customer selects answers for each question
5. System saves preferences using `/customer/preference` endpoint
6. Customer can now get personalized recommendations

### Product Addition Flow
1. Provider logs in
2. Provider creates product with basic details
3. Provider answers the same questions but from product perspective
4. System creates ProductUsages entries linking product to question-option pairs
5. Product becomes available for matching with customer preferences

### Recommendation Flow
1. Customer requests recommendations
2. System fetches customer's preferences (option IDs)
3. System finds products with matching ProductUsages
4. Products are scored based on number of matching preferences
5. Results are sorted by match score and returned

## Key Features

- **Flexible Question System**: Admins can add/modify questions anytime
- **Preference Completion Tracking**: System tracks which questions customers haven't answered
- **Match Scoring**: Products are ranked by how many preferences they match
- **Pagination Support**: Large result sets are properly paginated
- **Real-time Updates**: Preferences can be updated anytime and recommendations adjust immediately

## Usage Examples

### Example Questions
1. "What type of cuisine do you prefer?" (Italian, Chinese, Indian, Mexican)
2. "What's your preferred meal time?" (Breakfast, Lunch, Dinner, Snacks)
3. "Do you prefer spicy food?" (Yes, No, Sometimes)
4. "What's your budget range?" (Budget, Mid-range, Premium)
5. "Do you have dietary restrictions?" (None, Vegetarian, Vegan, Gluten-free)

### Example Product Matching
A "Spicy Italian Pasta" product might match:
- Question 1: Italian (cuisine type)
- Question 2: Dinner (meal time)  
- Question 3: Yes (spicy preference)
- Question 4: Mid-range (budget)
- Question 5: None (no dietary restrictions)

Customers who selected these same preferences would see this product in their recommendations with a high match score.
