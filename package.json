{"name": "beepr-backend", "version": "1.7.0", "description": "Create a Node.js app for building production-ready RESTful APIs using Express, by running one command", "bin": "bin/createNodejsApp.js", "main": "src/index.js", "repository": "https://github.com/hagopj13/node-express-boilerplate.git", "author": "<PERSON><PERSON><PERSON> <github@ridhijain1>", "license": "MIT", "engines": {"node": ">=12.0.0"}, "scripts": {"start": "pm2 start ecosystem.config.json --no-daemon", "dev": "cross-env NODE_ENV=development nodemon src/index.js", "test": "jest -i --colors --verbose --detect<PERSON><PERSON><PERSON>andles", "test:watch": "jest -i --watchAll", "coverage": "jest -i --coverage", "coverage:coveralls": "jest -i --coverage --coverageReporters=text-lcov | coveralls", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check **/*.js", "prettier:fix": "prettier --write **/*.js", "docker:prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:test": "docker-compose -f docker-compose.yml -f docker-compose.test.yml up", "prepare": "husky install"}, "keywords": ["node", "node.js", "boilerplate", "generator", "express", "rest", "api", "mongodb", "mongoose", "es6", "es7", "es8", "es9", "jest", "travis", "docker", "passport", "joi", "eslint", "prettier"], "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "cross-env": "^7.0.0", "dotenv": "^10.0.0", "express": "^4.17.1", "express-mongo-sanitize": "^2.0.0", "express-rate-limit": "^5.0.0", "firebase": "^11.6.1", "helmet": "^4.1.0", "http-status": "^1.4.0", "joi": "^17.3.0", "jsonwebtoken": "^8.5.1", "moment": "^2.24.0", "mongoose": "^5.7.7", "mongoose-delete": "^1.0.2", "morgan": "^1.9.1", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "nodemailer": "^6.3.1", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "pm2": "^5.1.0", "swagger-jsdoc": "^6.0.8", "swagger-ui-express": "^4.1.6", "validator": "^13.0.0", "winston": "^3.2.1", "xss-clean": "^0.1.1"}, "devDependencies": {"coveralls": "^3.0.7", "eslint": "^7.0.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^24.0.1", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-security": "^1.4.0", "faker": "^5.1.0", "husky": "7.0.4", "jest": "^26.0.1", "lint-staged": "^11.0.0", "node-mocks-http": "^1.8.0", "nodemon": "^2.0.0", "prettier": "^2.0.5", "supertest": "^6.0.1"}}