import React from 'react';
import { But<PERSON> } from 'primereact/button';
import { requestPermissionAndGetToken } from '../apis/permission';

const NotificationSetup: React.FC = () => {
  const handleRequestPermission = async () => {
    try {
      await requestPermissionAndGetToken();
    } catch (error) {
      console.error('Error requesting permission:', error);
    }
  };

  return (
    <div className="p-4">
      <h3>Notification Setup</h3>
      <div className="flex gap-2 mt-3">
        <Button
          label="Enable Notifications"
          onClick={handleRequestPermission}
          className="p-button-primary"
        />
      </div>
      <div className="mt-3">
        <p><strong>Instructions:</strong></p>
        <ol>
          <li>Click "Enable Notifications" to setup push notifications</li>
          <li>Allow notifications when prompted by your browser</li>
          <li>You'll now receive notifications when customers are nearby!</li>
        </ol>
        
        <div className="mt-4 p-3 bg-green-100 rounded">
          <p><strong>✅ Notification System Ready!</strong></p>
          <p>Your app is now configured to receive push notifications from the backend.</p>
        </div>
      </div>
    </div>
  );
};

export default NotificationSetup;
