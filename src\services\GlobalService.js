// Required modules and utilities
const { getModel } = require('../models');
const { ObjectId } = require('mongodb');

/**
 * Create a new resource in the specified model.
 * @param {string} modelName - Name of the model.
 * @param {Object} body - Data for the new resource.
 * @returns {Promise<Object>} - Created resource.
 */
const create = async (modelName, body) => {
  const Model = getModel(modelName);
  console.log('modelName:', modelName);

  return Model.create(body);
};

/**
 * Query for resources with pagination.
 * @param {string} modelName - Name of the model.
 * @param {Object} options - Query options.
 * @param {string} [options.sortBy] - Sort option in the format: field:(desc|asc) (default = asc).
 * @param {number} [options.limit] - Maximum number of results per page (default = 10).
 * @param {number} [options.page] - Current page (default = 1).
 * @param {Array} [aggregation] - Additional aggregation pipeline stages.
 * @returns {Promise<Object>} - Paginated query result.
 */
const getAll = async (modelName, options, aggregation = []) => {
  const Model = getModel(modelName);
  const data = await Model.paginate({ ...options }, aggregation);
  return data;
};

/**
 * Query for resources without pagination.
 * @param {string} modelName - Name of the model.
 * @param {Array} [aggregation] - Aggregation pipeline stages.
 * @returns {Promise<Array>} - List of resources.
 */
const getAllWithoutPagination = async (modelName, aggregation = []) => {
  const Model = getModel(modelName);
  const data = await Model.aggregate(aggregation).exec();
  return data;
};

/**
 * Retrieve a resource by its ID without additional aggregation.
 * @param {string} modelName - Name of the model.
 * @param {string|ObjectId} id - Resource ID.
 * @returns {Promise<Object|null>} - Resource or null if not found.
 */
const getByIdWithoutAggregation = async (modelName, id) => {
  id = id instanceof ObjectId ? id : ObjectId(id);
  const Model = getModel(modelName);
  const result = await Model.findById(id);
  return result;
};

/**
 * Retrieve a resource by its ID with additional aggregation.
 * @param {string} modelName - Name of the model.
 * @param {string|ObjectId} id - Resource ID.
 * @param {Array} [aggregation] - Aggregation pipeline stages.
 * @returns {Promise<Object|null>} - Resource or null if not found.
 */
const getById = async (modelName, id, aggregation = []) => {
  id = id instanceof ObjectId ? id : ObjectId(id);
  const Model = getModel(modelName);
  const pipeline = [{ $match: { _id: id } }, ...aggregation];
  const result = await Model.aggregate(pipeline).exec();
  return result.length ? result[0] : null;
};

const getOne = async (modelName, filter) => {
  const Model = getModel(modelName);
  if (!Model) throw new Error(`Model ${modelName} not found`);
  
  const result = await Model.findOne(filter).exec();
  return result;
};

const getAllByFilter = async (modelName, filter, aggregation = []) => {
  const Model = getModel(modelName);
  if (!Model) throw new Error(`Model ${modelName} not found`);

  // If you have aggregation, apply $match + aggregation
  if (aggregation.length) {
    return Model.aggregate([
      { $match: filter },
      ...aggregation,
    ]);
  }
  return Model.find(filter);
};


/**
 * Update a resource by its ID.
 * @param {string} modelName - Name of the model.
 * @param {string|ObjectId} id - Resource ID.
 * @param {Object} body - Updated data.
 * @param {Array} [aggregation] - Aggregation pipeline stages.
 * @returns {Promise<Object>} - Updated resource.
 */
const updateById = async (modelName, id, body, aggregation = []) => {
  const resource = await getByIdWithoutAggregation(modelName, id);
  Object.assign(resource, body);
  await resource.save();
  const result = await getById(modelName, id, aggregation);
  return result;
};

/**
 * Toggle the status of a resource by its ID.
 * @param {string} modelName - Name of the model.
 * @param {string|ObjectId} id - Resource ID.
 * @returns {Promise<Object>} - Updated resource.
 */
const toggleStatusById = async (modelName, id) => {
  let resource = await getByIdWithoutAggregation(modelName, id);
  resource.status = !resource.status;
  resource = await resource.save();
  return resource;
};

/**
 * Soft delete a resource by its ID.
 * Marks unique fields with a random identifier and removes the resource.
 * @param {string} modelName - Name of the model.
 * @param {Array<string>} uniqueFields - List of unique fields to mark.
 * @param {string|ObjectId} id - Resource ID.
 * @returns {Promise<Object>} - Deleted resource.
 */
const softDeleteById = async (modelName, uniqueFields, id) => {
  const resource = await getByIdWithoutAggregation(modelName, id);
  uniqueFields.forEach((uniqueField) => {
    const fieldType = typeof resource[uniqueField];
    if (fieldType === 'string') {
      // For strings, generate a random alphanumeric string
      resource[uniqueField] = `#${Math.random().toString(36).substring(2, 15)}`;
    } else if (fieldType === 'number') {
      // For numbers, combine the current timestamp with a random number for uniqueness
      resource[uniqueField] = Date.now() + Math.floor(Math.random() * 1000000);
    } else {
      // Fallback: if the type isn't recognized, default to a random string
      resource[uniqueField] = `#${Math.random().toString(36).substring(2, 15)}`;
    }
  });
  await resource.save();
  await resource.delete();
  return resource;
};

/**
 * Find resources by column name(s) without aggregation.
 * @param {string} modelName - Name of the model.
 * @param {Object} filter - Filter to match column(s) and value(s).
 * @returns {Promise<Array>} - Matched resources.
 */
const findByColumnName = async (modelName, filter) => {
  const Model = getModel(modelName);
  const data = await Model.find(filter).exec();
  return data[0];
};

/**
 * Create or update a resource based on given parameters.
 * If the resource exists (based on provided parameters), it will be updated.
 * If the resource does not exist, it will be created.
 * @param {string} modelName - Name of the model.
 * @param {Object} params - Parameters to find the existing resource (can include multiple fields).
 * @param {Object} body - Data for the resource.
 * @returns {Promise<Object>} - Created or updated resource.
 */
const createOrUpdate = async (modelName, params, data) => {
  const Model = getModel(modelName);
  let resource = await Model.findOne(params);

  if (resource) {
    // Update the resource if it exists
    Object.assign(resource, data);
    await resource.save();
    return resource;
  }

  // Create a new resource if no matching resource is found
  resource = await Model.create(data);
  return resource;
};

// /**
//  * Find resources by column name(s) and value(s).
//  * @param {string} modelName - Name of the model.
//  * @param {Object} filter - Filter object with column name(s) and value(s).
//  * @param {Array} [aggregation] - Additional aggregation pipeline stages.
//  * @returns {Promise<Array>} - List of matching resources.
//  */
// const findByColumnName = async (modelName, filter, aggregation = []) => {
//   const Model = getModel(modelName);
//   const pipeline = [{ $match: filter }, ...aggregation];
//   const result = await Model.aggregate(pipeline).exec();
//   return result;
// };

// /**
//  * Update resources based on a filter.
//  * @param {string} modelName - Name of the model.
//  * @param {Object} filter - Filter object to find the resources to update.
//  * @param {Object} body - Updated data.
//  * @param {Array} [aggregation] - Additional aggregation pipeline stages to retrieve updated data.
//  * @returns {Promise<Array>} - List of updated resources.
//  */
// const updateByFilter = async (modelName, filter, body, aggregation = []) => {
//   const Model = getModel(modelName);
//   const result = await Model.updateMany(filter, { $set: body }, { new: true }).exec();
//   const pipeline = [{ $match: filter }, ...aggregation];
//   const updatedData = await Model.aggregate(pipeline).exec();
//   return updatedData;
// };

// Export all services
module.exports = {
  create,
  getAll,
  getAllWithoutPagination,
  getById,
  getOne,
  getAllByFilter,
  updateById,
  toggleStatusById,
  softDeleteById,
  findByColumnName,
  createOrUpdate,
  getByIdWithoutAggregation
};
