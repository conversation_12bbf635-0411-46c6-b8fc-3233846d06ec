const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const GlobalService = require('../services/GlobalService');
const pick = require('../utils/pick');
const { userService } = require('../services');

const modelName = 'Provider';
const resourceName = 'Provider';
const resourcesName = 'Providers';
const aggregation = [
  {
    $lookup: {
      from: 'users',
      localField: 'user',
      foreignField: '_id',
      as: 'userDetails',
    },
  },
  {
    $addFields: {
      email: { $arrayElemAt: ['$userDetails.email', 0] },
    },
  },
  {
    $project: {
      userDetails: 0,
    },
  },
];

const create = catchAsync(async (req, res) => {
  let userData = pick(req.body, ['email', 'password']);
  userData['role'] = 'provider';
  const user = await userService.createUser(userData);
  req.body['user'] = user._id;

  if (req.body.latitude && req.body.longitude) {
    req.body.location = {
      type: 'Point',
      coordinates: [parseFloat(req.body.longitude), parseFloat(req.body.latitude)],
    };
    delete req.body.latitude;
    delete req.body.longitude;
  }
  const provider = await GlobalService.create(modelName, req.body);
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, provider);
});

const index = catchAsync(async (req, res) => {
  const options = { ...req.query };
  let pipeline = [...aggregation];

  if (req.customer && req.customer._id) {
    pipeline.push(
      { $match: { isApproved: "approved" } },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'provider',
          as: 'products',
        },
      },
      // Unwind the products array to perform lookups for categories
      { $unwind: { path: '$products', preserveNullAndEmptyArrays: true } },
      // Lookup to fetch category details for each product
      {
        $lookup: {
          from: 'categories',
          localField: 'products.category',
          foreignField: '_id',
          as: 'products.categoryDetails',
        },
      },
      // Add the category name to each product
      {
        $addFields: {
          'products.categoryName': { $arrayElemAt: ['$products.categoryDetails.name', 0] },
        },
      },
      // Group products back into an array, grouped by provider
      {
        $group: {
          _id: '$_id',
          root: { $first: '$$ROOT' },
          products: { $push: '$products' },
        },
      },
      // Replace the root with the original document and add the grouped products
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: ['$root', { products: '$products' }],
          },
        },
      },
      // Group products by category and include the category name
      {
        $addFields: {
          productsByCategory: {
            $reduce: {
              input: '$products',
              initialValue: [],
              in: {
                $cond: [
                  { $in: ['$$this.category', '$$value.category'] },
                  {
                    $map: {
                      input: '$$value',
                      as: 'cat',
                      in: {
                        $cond: [
                          { $eq: ['$$cat.category', '$$this.category'] },
                          {
                            category: '$$cat.category',
                            categoryName: '$$cat.categoryName',
                            products: { $concatArrays: ['$$cat.products', ['$$this']] },
                          },
                          '$$cat',
                        ],
                      },
                    },
                  },
                  {
                    $concatArrays: [
                      '$$value',
                      [
                        {
                          category: '$$this.category',
                          categoryName: '$$this.categoryName',
                          products: ['$$this'],
                        },
                      ],
                    ],
                  },
                ],
              },
            },
          },
        },
      },
      // Remove the individual products array if you only want the grouped data
      {
        $project: {
          products: 0,
        },
      }
    );
  }

  const providers = await GlobalService.getAll(modelName, options, pipeline);
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, providers);
});

const view = catchAsync(async (req, res) => {
  // Handle different parameter names from different routes
  const providerId = req.params.provider || req.params.providerId || req.params.id;

  if (!providerId) {
    return sendError(res, 'Provider ID is required', httpStatus.BAD_REQUEST);
  }

  console.log('Looking for provider with ID:', providerId); // Debug log

  const provider = await GlobalService.getById(modelName, providerId, aggregation);
  if (!provider) {
    return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  }
  sendSuccess(res, `${resourceName} fetched successfully!`, httpStatus.OK, provider);
});

const update = catchAsync(async (req, res) => {
  // 1. Handle coordinates conversion
  if (req.body.latitude || req.body.longitude) {
    // Validate presence of both fields
    if (!req.body.latitude || !req.body.longitude) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Both latitude and longitude are required');
    }

    // Parse and validate coordinates
    const longitude = parseFloat(req.body.longitude);
    const latitude = parseFloat(req.body.latitude);

    if (isNaN(longitude) || isNaN(latitude)) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid coordinate values');
    }

    // Create GeoJSON point
    req.body.location = {
      type: 'Point',
      coordinates: [longitude, latitude]
    };

    delete req.body.latitude;
    delete req.body.longitude;
  }

  try {
    // 2. Update provider with validation
    const provider = await GlobalService.updateById(
      modelName,
      req.params.id, // Typically better to use params than body for ID
      req.body,
      aggregation,
      { runValidators: true } // ENABLE VALIDATORS
    );

    // 3. Update associated user email if needed
    if (req.body.email) {
      await GlobalService.updateById(
        "User",
        provider.user,
        { email: req.body.email }
      );
    }

    sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, provider);
  } catch (error) {
    // Handle validation errors specifically
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(el => el.message);
      throw new ApiError(httpStatus.BAD_REQUEST, `Validation failed: ${errors.join(', ')}`);
    }
    throw error;
  }
});

const softDelete = catchAsync(async (req, res) => {
  const provider = await GlobalService.getById(modelName, req.params.provider, aggregation);
  if (!provider) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  await GlobalService.softDeleteById('User', ['email'], provider.user);
  await GlobalService.softDeleteById(modelName, [], req.params.provider);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

const status = catchAsync(async (req, res) => {
  const provider = await GlobalService.updateById(modelName, req.body.provider, pick(req.body, ['isApproved']));
  sendSuccess(res, `${resourceName} status changed successfully!`, httpStatus.OK, provider);
});

const updateProvider = catchAsync(async (req, res) => {
  req.body['user'] = req.user._id;
  let provider;
  if (req.provider._id) provider = await GlobalService.updateById(modelName, req.provider._id, req.body, aggregation);
  else provider = await GlobalService.create(modelName, req.body);
  if (req.body.email) await GlobalService.updateById('User', req.user._id, pick(req.body, ['email']));
  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, provider);
});

module.exports = { create, index, view, update, softDelete, status, updateProvider };
