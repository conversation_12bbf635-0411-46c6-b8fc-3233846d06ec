const Joi = require('joi');
const httpStatus = require('http-status');
const pick = require('../utils/pick');
const { sendError } = require('../utils/ApiResponse');

const validate = (schema) => async (req, res, next) => {
  try {
    const validSchema = pick(schema, ['params', 'query', 'body']);
    const object = pick(req, Object.keys(validSchema));

    const { value, error } = await Joi.compile(validSchema)
      .prefs({ errors: { label: 'key' }, abortEarly: false })
      .validateAsync(object);

    if (error) {
      const data = error.details.reduce((acc, curr) => {
        const field = curr.path[curr.path.length - 1];
        acc[field] = curr.message;
        return acc;
      }, {});

      const firstMessage = error.details[0]?.message || 'Validation error occurred.';
      return sendError(res, firstMessage, httpStatus.BAD_REQUEST, data);
    }

    Object.assign(req, value);
    next();
  } catch (error) {
    if (error instanceof Joi.ValidationError) {
      const data = error.details.reduce((acc, curr) => {
        const field = curr.path[curr.path.length - 1];
        acc[field] = curr.message;
        return acc;
      }, {});
      const firstMessage = error.details[0]?.message || 'Validation error occurred.';
      return sendError(res, firstMessage, httpStatus.BAD_REQUEST, data);
    }
    console.error('Unexpected validation error:', error);
    return sendError(res, 'Internal server error', httpStatus.INTERNAL_SERVER_ERROR);
  }
};

module.exports = validate;
