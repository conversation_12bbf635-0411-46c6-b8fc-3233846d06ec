import api from './index';

/**
 * Logs in a user with the provided email and password.
 *
 * @param {string} email - The email address of the user.
 * @param {string} password - The password of the user.
 * @returns {Promise<any>} A promise that resolves to the response data containing authentication tokens or user details.
 */
export const login = async (email: string, password: string): Promise<any> => {
    const response = await api.post('auth/login', { email, password });
    return response.data;
};

/**
 * Refreshes authentication tokens using the provided refresh token.
 *
 * @param {string} refreshToken - The refresh token used to obtain new access tokens.
 * @returns {Promise<any>} A promise that resolves to the response data containing the new authentication tokens.
 */
export const refresh = async (refreshToken: string): Promise<any> => {
    const response = await api.post('auth/refresh-tokens', { refreshToken });
    return response.data;
};

/**
 * Logs out a user by invalidating the provided refresh token.
 *
 * @param {string} refreshToken - The refresh token to be invalidated.
 * @returns {Promise<any>} A promise that resolves to the response data confirming the logout operation.
 */
export const logout = async (refreshToken: string): Promise<any> => {
    const response = await api.post('auth/logout', { refreshToken });
    return response.data;
};

/**
 * Registers a new user with the given email, password, and role.
 *
 * @param {string} email - The email address for the new user.
 * @param {string} password - The password for the new user.
 * @param {string} role - The role to be assigned to the new user (e.g., 'user', 'admin').
 * @returns {Promise<any>} A promise that resolves to the response data confirming the registration.
 */
export const signup = async (email: string, password: string, role: string): Promise<any> => {
    const response = await api.post('auth/register', { email, password, role });
    return response.data;
};
