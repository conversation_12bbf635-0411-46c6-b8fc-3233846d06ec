import { useState } from 'react';
import { LazyParams } from '../types/global';

const usePagination = (initialParams: LazyParams) => {
    const [params, setParams] = useState<LazyParams>(initialParams);

    const onPage = (event: any) => {
        setParams(prev => ({
            ...prev,
            page: (event.first / event.rows) + 1,
            limit: event.rows
        }));
    };

    const onSort = (event: any) => {
        setParams(prev => ({
            ...prev,
            sortField: event.sortField,
            sortOrder: event.sortOrder
        }));
    };

    const setSearch = (searchText: string) => {
        setParams(prev => ({
            ...prev,
            search: searchText,
            page: 1
        }));
    };

    return {
        params,
        onPage,
        onSort,
        setSearch
    };
};

export default usePagination;
