import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { But<PERSON> } from 'primereact/button';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { getProviders, createProvider, updateProvider, deleteProvider, updateProviderStatus } from '../../apis/admin';
import { catchAsync, handelFormData, handelResponse } from '../../utils/helper';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import { defaultLocation, Provider, ProviderFormData } from '../../types/global';
import { defaultPaginationValues } from '../../types/global';
import ProviderForm from '../../components/admin/ProviderForm';
import { Column } from 'primereact/column';
import usePagination from '../../hooks/usePagination';
import { Dialog } from 'primereact/dialog';
import GoogleMapComponent from '../../components/admin/GoogleMap';
import { FormikHelpers } from 'formik';

const Providers: React.FC = () => {
    const [providers, setProviders] = useState<Provider[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [loading, setLoading] = useState(false);
    const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues); // Use the hook
    const [searchText, setSearchText] = useState('');
    const [visibleDialog, setVisibleDialog] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
    const [formData, setFormData] = useState<ProviderFormData>({});
    const [descriptionPopupVisible, setDescriptionPopupVisible] = useState(false);
    const [locationPopupVisible, setLocationPopupVisible] = useState(false);
    const [documentPopupVisible, setDocumentPopupVisible] = useState(false);
    const [selectedDocumentUrl, setSelectedDocumentUrl] = useState<string | null>(null);

    useEffect(() => {
        fetchProviders();
    }, [params]);

    const fetchProviders = async () => {
        setLoading(true);
        catchAsync(
            async () => {
                const response = await getProviders({
                    page: params.page,
                    limit: params.limit,
                    sort: params.sortField && params.sortOrder ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}` : undefined,
                    search: params.search
                });
                setProviders(response.data.results);
                setTotalRecords(response.data.totalResults);
            }
        ).finally(() => setLoading(false));
    };

    const handleCreate = () => {
        setSelectedProvider(null);
        setFormData({});
        setEditMode(false);
        setVisibleDialog(true);
    };

    const handleEdit = (provider: Provider) => {
        setSelectedProvider(provider);
        setFormData(provider);
        setEditMode(true);
        setVisibleDialog(true);
    };

    const handleDelete = (providerId: string) => {
        confirmDialog({
            message: 'Are you sure you want to delete this provider?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                catchAsync(
                    async () => {
                        const response = await deleteProvider(providerId);
                        handelResponse(response);
                        if (response.status) fetchProviders();
                    }, { showToast: true }
                );
            }
        });
    };

    const handleStatusChange = async (provider: Provider) => {
        const newStatus = provider.isApproved === 'approved' ? 'rejected' : 'approved';
        catchAsync(
            async () => {
                const response = await updateProviderStatus(provider?._id ?? "", newStatus);
                handelResponse(response);
                if (response.status) fetchProviders();
            }, { showToast: true }
        );
    };

    const handleSubmit = async (values: ProviderFormData, actions: FormikHelpers<ProviderFormData>) => {
        const formDataToSend = handelFormData(values);

        catchAsync(
            async () => {
                let response;
                if (editMode) {
                    formDataToSend.append('provider', String(selectedProvider?._id));
                    if (formDataToSend.has('password')) {
                        formDataToSend.delete('password');
                    }
                    response = await updateProvider(formDataToSend);
                } else {
                    response = await createProvider(formDataToSend);
                }
                handelResponse(response, actions);
                if (response.status) {
                    setVisibleDialog(false);
                    fetchProviders();
                }
            }, { showToast: true }
        );
    };

    useEffect(() => {
        const timer = setTimeout(() => {
            setSearch(searchText); // Use setSearch from hook
        }, 500);
        return () => clearTimeout(timer);
    }, [searchText]);

    const actionBodyTemplate = (rowData: Provider) => {
        return (
            <div className="flex gap-2">
                <Button
                    icon="pi pi-pencil"
                    className="p-button-rounded p-button-success w-8 h-8"
                    onClick={() => handleEdit(rowData)}
                />
                <Button
                    icon="pi pi-trash"
                    className="p-button-rounded p-button-danger w-8 h-8"
                    onClick={() => handleDelete(rowData?._id ?? "")}
                />
                <Button
                    label={rowData.isApproved === 'approved' ? 'Reject' : 'Approve'}
                    className="p-button-rounded"
                    onClick={() => handleStatusChange(rowData)}
                />
            </div>
        );
    };

    const statusBodyTemplate = (rowData: Provider) => {
        const severity = rowData.isApproved === 'approved' ? 'success' :
            rowData.isApproved === 'pending' ? 'warning' : 'danger';
        return <span className={`p-tag p-tag-${severity}`}>{rowData.isApproved}</span>;
    };

    const handleDescriptionClick = (provider: Provider) => {
        setSelectedProvider(provider);
        setDescriptionPopupVisible(true);
    };

    const handleLocationClick = (provider: Provider) => {
        setSelectedProvider(provider);
        provider.latitude = Number(provider.latitude);
        provider.longitude = Number(provider.longitude);
        setLocationPopupVisible(true);
    };

    const handleDocumentClick = (documentUrl: string | null) => {
        if (documentUrl) {
            setSelectedDocumentUrl(documentUrl);
            setDocumentPopupVisible(true);
        }
    };


    return (
        <div className="card p-fluid">
            <ConfirmDialog />

            <div className="flex justify-content-between align-items-center mb-4">
                <h2>Providers Management</h2>
                <div className="flex gap-2">
                    <span className="p-input-icon-left">
                        <IconField iconPosition="left">
                            <InputIcon className="pi pi-search" />
                            <InputText
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                                placeholder="Search providers..."
                            />
                        </IconField>
                    </span>
                    <Button label="Create Provider" icon="pi pi-plus" onClick={handleCreate} />
                </div>
            </div>

            <DataTable value={providers} lazy paginator first={(params.page - 1) * params.limit} rows={params.limit}
                totalRecords={totalRecords} onPage={onPage} onSort={onSort} sortField={params.sortField} sortOrder={params.sortOrder}
                loading={loading} rowsPerPageOptions={[10, 20, 50]} className="p-datatable-striped" removableSort
            >
                <Column field="name" header="Name" sortable />
                <Column field="description" header="Description" body={(rowData) =>
                    <p title="View" className='cursor-pointer' onClick={() => handleDescriptionClick(rowData)} >
                        {rowData?.description.length > 42 ? rowData?.description.substring(0, 42 - 3) + "..." : rowData?.description}
                    </p>
                } />
                <Column field="email" header="Email" sortable />
                <Column field="paymentOption" header="Payment Methods" body={(data) => data.paymentOption.join(', ')} />
                <Column field="availableDays" header="Working Days" body={(data) => data.availableDays.join(', ')} />
                <Column field="isApproved" header="Status" body={statusBodyTemplate} sortable />
                <Column header="Documents" body={(rowData) => (
                    <div className="flex flex-col gap-2">
                        {rowData.photoId && <a className="text-blue-500 cursor-pointer" onClick={() => handleDocumentClick(rowData.photoId)}>Photo ID</a>}
                        {rowData.cannabisLicense && <a className="text-blue-500 cursor-pointer" onClick={() => handleDocumentClick(rowData.cannabisLicense)}>Cannabis License</a>}
                        {rowData.resellersPermit && <a className="text-blue-500 cursor-pointer" onClick={() => handleDocumentClick(rowData.resellersPermit)}>Resellers Permit</a>}
                    </div>
                )} />
                <Column
                    className='cursor-pointer'
                    field="address"
                    header="Address"
                    body={(rowData) => (
                        <p onClick={() => handleLocationClick(rowData)}>
                            {`${rowData.street}, ${rowData.city}, ${rowData.state}, ${rowData.country}, ${rowData.zipCode}`}
                        </p>
                    )}
                />
                <Column field="time" header="Time" body={(rowData) => `${rowData.startTime} - ${rowData.endTime}`} className='text-center w-[1rem]' />
                <Column header="Actions" body={actionBodyTemplate} />
            </DataTable>

            <ProviderForm
                visible={visibleDialog}
                onHide={() => setVisibleDialog(false)}
                onSubmit={handleSubmit}
                initialValues={formData}
                editMode={editMode}
            />

            {/* Description Popup */}
            <Dialog visible={descriptionPopupVisible} onHide={() => setDescriptionPopupVisible(false)} header="Description">
                <p>{selectedProvider?.description}</p>
            </Dialog>

            {/* Location Popup with Google Map */}
            <Dialog style={{ minWidth: '50vw' }} visible={locationPopupVisible} onHide={() => setLocationPopupVisible(false)} header="Location">
                <GoogleMapComponent radius={selectedProvider?.radius || defaultLocation.radius} lat={selectedProvider?.latitude || defaultLocation.latiude} lng={selectedProvider?.longitude || defaultLocation.longitude} />
            </Dialog>

            {/* Dialog to Display the Selected Document */}
            <Dialog visible={documentPopupVisible} onHide={() => setDocumentPopupVisible(false)} header="Document Viewer" style={{ minWidth: '60vw' }}>
                {selectedDocumentUrl ? (
                    <iframe src={selectedDocumentUrl} width="100%" height="500px" />
                ) : (
                    <p>No document available</p>
                )}
            </Dialog>
        </div>
    );
};

export default Providers;