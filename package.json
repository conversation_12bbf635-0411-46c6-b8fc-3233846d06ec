{"name": "front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-google-maps/api": "^2.20.5", "@tailwindcss/vite": "^4.0.0", "axios": "^1.7.9", "firebase": "^11.8.1", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primereact": "^10.9.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-router-dom": "^7.1.3", "react-toastify": "^11.0.3", "tailwindcss": "^4.0.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}