import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { <PERSON><PERSON> } from 'primereact/button';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Column } from 'primereact/column';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import usePagination from '../../hooks/usePagination';
import { getQuestions, createQuestion, updateQuestion, deleteQuestion, } from '../../apis/admin';
import { catchAsync, handelResponse } from '../../utils/helper';
import QuestionForm from '../../components/admin/QuestionForm';
import { Question, QuestionFormData } from '../../types/global';
import { defaultPaginationValues } from '../../types/global';
import { FormikHelpers } from 'formik';

const Questions: React.FC = () => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
  const [searchText, setSearchText] = useState('');
  const [visibleDialog, setVisibleDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [formData, setFormData] = useState<Partial<QuestionFormData>>({});

  useEffect(() => {
    fetchQuestions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  const fetchQuestions = async () => {
    setLoading(true);
    catchAsync(async () => {
      const response = await getQuestions({
        page: params.page,
        limit: params.limit,
        sort:
          params.sortField && params.sortOrder
            ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}`
            : undefined,
        search: params.search,
      });
      setQuestions(response.data.results);
      setTotalRecords(response.data.totalResults);
    }).finally(() => setLoading(false));
  };

  const handleCreate = () => {
    setSelectedQuestion(null);
    setFormData({});
    setEditMode(false);
    setVisibleDialog(true);
  };

  const handleEdit = (question: Question) => {
    setSelectedQuestion(question);
    setFormData(question);
    setEditMode(true);
    setVisibleDialog(true);
  };

  const handleDelete = (questionId: string) => {
    confirmDialog({
      message: 'Are you sure you want to delete this question?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        catchAsync(async () => {
          const response = await deleteQuestion(questionId);
          handelResponse(response);
          if (response.status) fetchQuestions();
        }, { showToast: true });
      },
    });
  };

  const handleSubmit = async (values: QuestionFormData, actions: FormikHelpers<QuestionFormData>) => {
    catchAsync(async () => {
      let response;
      if (editMode) {
        values.question = selectedQuestion?._id;
        response = await updateQuestion(values);
      } else {
        response = await createQuestion(values);
      }
      handelResponse(response, actions);
      if (response.status) {
        setVisibleDialog(false);
        fetchQuestions();
      }
    }, { showToast: true });
  };

  // --- Table templates ---
  const questionBodyTemplate = (rowData: Question) => rowData.value;
  const sequenceBodyTemplate = (rowData: Question) => rowData.sequence;
  const optionsBodyTemplate = (rowData: Question) => {
    if (Array.isArray(rowData.options)) {
      return rowData.options.map((opt) => opt.value).join(', ');
    }
    return '';
  };
  const actionBodyTemplate = (rowData: Question) => (
    <div className="flex gap-2">
      <Button
        icon="pi pi-pencil"
        className="p-button-rounded p-button-success"
        onClick={() => handleEdit(rowData)}
      />
      <Button
        icon="pi pi-trash"
        className="p-button-rounded p-button-danger"
        onClick={() => handleDelete(rowData._id)}
      />
    </div>
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(searchText);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchText]);

  return (
    <div className="card p-fluid">
      <ConfirmDialog />

      <div className="flex justify-content-between align-items-center mb-4">
        <h2>Questions Management</h2>
        <div className="flex gap-2">
          <span className="p-input-icon-left">
            <IconField iconPosition="left">
              <InputIcon className="pi pi-search" />
              <InputText
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                placeholder="Search questions..."
              />
            </IconField>
          </span>
          <Button label="Create Question" icon="pi pi-plus" onClick={handleCreate} />
        </div>
      </div>

      <DataTable
        value={questions}
        lazy
        paginator
        first={(params.page - 1) * params.limit}
        rows={params.limit}
        totalRecords={totalRecords}
        onPage={onPage}
        onSort={onSort}
        sortField={params.sortField}
        sortOrder={params.sortOrder}
        loading={loading}
        rowsPerPageOptions={[10, 20, 50]}
        className="p-datatable-striped"
        removableSort
      >
        <Column field="value" header="Question" body={questionBodyTemplate} sortable />
        <Column field="sequence" header="Sequence" body={sequenceBodyTemplate} sortable />
        <Column field="options" header="Options" body={optionsBodyTemplate} />
        <Column header="Actions" body={actionBodyTemplate} />
      </DataTable>

      <QuestionForm
        visible={visibleDialog}
        onHide={() => setVisibleDialog(false)}
        onSubmit={handleSubmit}
        initialValues={formData}
        editMode={editMode}
      />
    </div>
  );
};

export default Questions;
