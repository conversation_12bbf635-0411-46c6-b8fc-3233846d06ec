// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getMessaging } from "firebase/messaging"; // Import getMessaging for Firebase Cloud Messaging

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB55FVfkuqRwmyi01c6PRxXvgq-_ulHusM",
  authDomain: "notification-51871.firebaseapp.com",
  projectId: "notification-51871",
  storageBucket: "notification-51871.firebasestorage.app",
  messagingSenderId: "124892789819",
  appId: "1:124892789819:web:b517ea02f3b18cf2c5b01e",
  measurementId: "G-FZD0Y6CJJ1"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and export it
// Initialize messaging directly - it will be available in browser environment
export const messaging = getMessaging(app);

// If you want to use analytics, uncomment the following lines
// import { getAnalytics } from "firebase/analytics";
// const analytics = getAnalytics(app);