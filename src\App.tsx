import { BrowserRouter, Route, Routes } from 'react-router-dom'
import './App.css'
import { AuthProvider } from './utils/AuthContext'
import { useEffect } from 'react'
import { setupForegroundMessageListener } from './apis/permission'
import AdminLayout from './layouts/AdminLayout'
import AuthenticatedRoute from './utils/AuthenticatedRoute'
import ProtectedRoute from './utils/ProtectedRoute'
import ProviderLayout from './layouts/ProviderLayout'
import DefaultLayout from './layouts/DefaultLayout'
import Login from './pages/Login'
import Home from './pages/Home'
import Dashboard from './pages/provider/Dashboard'
import AdminDashboard from "./pages/admin/Dashboard";
import AdminQuestions from "./pages/admin/Questions";
import AdminCategories from "./pages/admin/Categories";
import AdminProviders from "./pages/admin/Providers";
import AdminCustomers from "./pages/admin/Customers";
import AdminProducts from "./pages/admin/Products";
import Products from './pages/provider/Products';
import ProviderOrders from './pages/provider/Orders';
import ProviderReviews from './pages/provider/Reviews';
import AdminOrders from './pages/admin/Orders';
import AdminReviews from './pages/admin/Reviews';
import { ToastContainer } from 'react-toastify'
import Signup from './pages/Signup'
import Profile from './pages/provider/Profile'

function App() {
  // Setup foreground message listener when app loads
  useEffect(() => {
    // Always setup the message listener regardless of login status
    // This ensures we can receive notifications even if user logs in later
    console.log('Setting up Firebase message listener...');
    setupForegroundMessageListener();

    // Register service worker for background notifications
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/firebase-messaging-sw.js')
        .then((registration) => {
          console.log('Service Worker registered successfully:', registration);
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error);
        });
    }
  }, []);

  return (
    <AuthProvider>
      <BrowserRouter>
        <ToastContainer position="top-right" autoClose={2000} hideProgressBar newestOnTop closeOnClick rtl={false} pauseOnFocusLoss draggable pauseOnHover />
        <Routes>
          {/* Authentication Routes */}
          <Route element={<AuthenticatedRoute />}>
            {/* Restricted routes for logged in user */}
            <Route path="/" element={<DefaultLayout />}>
              <Route path="login" element={<Login />} />
              <Route path="signup" element={<Signup />} />
            </Route>
          </Route>

          {/* Unrestricted Routes */}
          <Route path="/" element={<DefaultLayout />}>
            <Route index element={<Home />} />
          </Route>

          {/* Provider Protected Routes */}
          <Route element={<ProtectedRoute />}>
            <Route path="/" element={<ProviderLayout />}>
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="products" element={<Products />} />
              <Route path="orders" element={<ProviderOrders />} />
              <Route path="reviews/:providerId" element={<ProviderReviews />} />
              <Route path="profile" element={<Profile />} />
            </Route>
          </Route>

          {/* Admin Protected Routes */}
          <Route element={<ProtectedRoute />}>
            <Route path="/admin" element={<AdminLayout />}>
              <Route index element={<AdminDashboard />} />
              <Route path="categories" element={<AdminCategories />} />
              <Route path="questions" element={<AdminQuestions />} />
              <Route path="customers" element={<AdminCustomers />} />
              <Route path="providers" element={<AdminProviders />} />
              <Route path="products" element={<AdminProducts />} />
              <Route path="orders" element={<AdminOrders />} />
              <Route path="reviews" element={<AdminReviews />} />
            </Route>
          </Route>
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  )
}

export default App
