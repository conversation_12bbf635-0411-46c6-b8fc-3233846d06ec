import { useEffect, useRef, useState } from 'react';
import { Link, Outlet, useNavigate, useLocation } from 'react-router-dom';
import { Menu } from 'primereact/menu';
import { useAuth } from '../utils/AuthContext';
import { showSuccess, catchAsync } from '../utils/helper';
import { getProfile } from '../apis/provider';
import Logo from "../assets/logo.svg";
import 'primeicons/primeicons.css';

const Layout = () => {
    const { logout, isActiveProvider } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();

    const menuLeft = useRef<Menu>(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [providerId, setProviderId] = useState<string | null>(null);

    const navItems = isActiveProvider
        ? [
            { name: 'Dashboard', icon: 'pi pi-home', href: '/dashboard' },
            { name: 'Products', icon: 'pi pi-box', href: '/products' },
            { name: 'Orders', icon: 'pi pi-shopping-cart', href: '/orders' },
            ...(providerId ? [{ name: 'Reviews', icon: 'pi pi-star', href: `/reviews/${providerId}` }] : []),
        ]
        : [];

    const dynamicMenuItems = [
        { label: 'Profile', icon: 'pi pi-user', command: () => handleProfile() },
        { label: 'Logout', icon: 'pi pi-sign-out', command: () => handleLogout() },
    ];

    useEffect(() => {
        if (!isActiveProvider) navigate('/profile');
    }, [isActiveProvider, navigate]);

    useEffect(() => {
        const fetchProviderId = async () => {
            if (isActiveProvider) {
                catchAsync(async () => {
                    const response = await getProfile();
                    if (response.status && response.data._id) {
                        setProviderId(response.data._id);
                    }
                }, { showToast: false });
            }
        };

        fetchProviderId();
    }, [isActiveProvider]);

    const handleProfile = () => {
        navigate('/profile');
    };

    const handleLogout = () => {
        logout();
        showSuccess("Logged out successfully");
    };

    return (
        <div className="h-screen flex flex-col bg-gray-900">
            {/* Top Navbar */}
            <nav className="fixed top-0 left-0 right-0 h-16 bg-gray-800 shadow-md z-10 flex items-center px-4 text-white">
                <button
                    onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                    className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                >
                    <span className="text-sm p-2">
                        <i className="pi pi-align-left"></i>
                    </span>
                </button>
                <h4 className="ml-2 font-semibold">Beepr</h4>
                <div className="flex-1" />
                <div className="flex items-center">
                    <Menu
                        model={dynamicMenuItems}
                        popup
                        ref={menuLeft}
                        id="popup_menu_left"
                        className="bg-gray-800 border border-gray-700"
                    />
                    <div
                        className="h-10 w-10 rounded-full cursor-pointer overflow-hidden"
                        onClick={(event) => menuLeft.current?.toggle(event)}
                        aria-controls="popup_menu_left"
                        aria-haspopup
                    >
                        <img className="h-full w-full object-cover" src={Logo} alt="Profile" />
                    </div>
                </div>
            </nav>

            <div className="flex flex-1 pt-16">
                {/* Sidebar */}
                <aside
                    className={`bg-gray-800 shadow-lg transform transition-all duration-300 fixed left-0 bottom-0 top-16 z-20 overflow-y-auto ${
                        isSidebarOpen ? 'w-48 translate-x-0' : '-translate-x-full'
                    } md:translate-x-0`}
                >
                    <div className="p-4 space-y-2">
                        {navItems.map((item) => (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`flex items-center p-3 rounded-lg group transition-colors ${
                                    location.pathname === item.href
                                        ? 'bg-[#CE93D8] text-[#121212]'
                                        : 'text-gray-300 hover:bg-gray-700'
                                }`}
                            >
                                <i className={`${item.icon} h-5 ${isSidebarOpen ? 'mr-3' : 'mr-0'}`}></i>
                                <span
                                    className={`${isSidebarOpen ? 'opacity-100 inline' : 'opacity-0 hidden'} transition`}
                                >
                                    {item.name}
                                </span>
                            </Link>
                        ))}
                    </div>
                </aside>

                {/* Main Content */}
                <main
                    className={`flex-1 transition-all duration-300 ${
                        isSidebarOpen ? 'md:ml-48' : 'md:ml-28'
                    } p-4 bg-gray-900`}
                >
                    <Outlet />
                </main>
            </div>
        </div>
    );
};

export default Layout;
